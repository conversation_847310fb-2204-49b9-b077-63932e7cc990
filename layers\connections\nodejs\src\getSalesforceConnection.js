const AWS = require('aws-sdk');
const jsforce = require('jsforce');

const s3 = new AWS.S3();

// Configuration for different Salesforce instances
const SALESFORCE_CONFIGS = {
  // Original Salesforce instance (default)
  default: {
    bucket: 'salesforce-authentication',
    keys: {
      application: {
        dev: 'sandbox/salesforce-client_secret.json',
        fullsbx: 'sandbox/salesforce-client_secret.json',
        backup: 'backup/salesforce-client_secret.json',
        prod: 'production/salesforce-client_secret.json',
      },
      access: {
        dev: 'sandbox/SBXaccessTokenEncrypted',
        fullsbx: 'sandbox/SBXaccessTokenEncrypted',
        backup: 'backup/SBXaccessTokenEncrypted',
        prod: 'production/PRODaccessTokenEncrypted',
      },
      refresh: {
        dev: 'sandbox/SBXrefreshTokenEncrypted',
        fullsbx: 'sandbox/SBXrefreshTokenEncrypted',
        backup: 'backup/SBXrefreshTokenEncrypted',
        prod: 'production/PRODrefreshTokenEncrypted',
      },
    }
  },
  // Second Salesforce instance
  salesforceCT: {
    bucket: 'salesforce-two-authentication',
    keys: {
      application: {
        dev: 'sandbox/salesforce-client_secret.json',
        fullsbx: 'sandbox/salesforce-client_secret.json',
        prod: 'production/salesforce-client_secret.json',
      },
      access: {
        dev: 'sandbox/SBXaccessTokenEncrypted',
        fullsbx: 'sandbox/SBXaccessTokenEncrypted',
        prod: 'production/PRODaccessTokenEncrypted',
      },
      refresh: {
        dev: 'sandbox/SBXrefreshTokenEncrypted',
        fullsbx: 'sandbox/SBXrefreshTokenEncrypted',
        prod: 'production/PRODrefreshTokenEncrypted',
      },
    }
  }
};

const getSalesforceInstance = async (environment, instanceType = 'default') => {
  const errorCallback = (error) => {
    if (error) throw error;
  };

  // Get configuration for the specified instance type
  const config = SALESFORCE_CONFIGS[instanceType];
  if (!config) {
    throw new Error(`Unknown Salesforce instance type: ${instanceType}`);
  }

  const { bucket, keys } = config;

  const sfApplicationParams = {
    Bucket: bucket,
    Key: keys.application[environment],
  };

  const applicationResponse = await s3
    .getObject(sfApplicationParams, errorCallback)
    .promise();
  // create Oauth2 object
  const appInfo = JSON.parse(applicationResponse.Body.toString());
  const {
    client_secret: clientSecret,
    client_id: clientId,
    loginUrl,
    redirectUri,
    instanceUrl,
  } = appInfo;

  const oauth2 = new jsforce.OAuth2({
    loginUrl,
    clientId,
    clientSecret,
    redirectUri,
  });

  const sfAccessParams = {
    Bucket: bucket,
    Key: keys.access[environment],
  };
  const accessResponse = await s3
    .getObject(sfAccessParams, errorCallback)
    .promise();
  const accessTokenParams = { CiphertextBlob: accessResponse.Body };

  const sfRefreshParams = {
    Bucket: bucket,
    Key: keys.refresh[environment],
  };
  const refreshResponse = await s3
    .getObject(sfRefreshParams, errorCallback)
    .promise();
  const refreshTokenParams = { CiphertextBlob: refreshResponse.Body };

  const kms = new AWS.KMS({ region: 'us-east-1' });

  const { Plaintext: decryptedRefresh } = await kms
    .decrypt(refreshTokenParams, errorCallback)
    .promise();
  const { Plaintext: decryptedAccess } = await kms
    .decrypt(accessTokenParams, errorCallback)
    .promise();

  const accessToken = decryptedAccess.toString('ascii');
  const refreshToken = decryptedRefresh.toString('ascii');

  const salesforceConnection = new jsforce.Connection({
    oauth2,
    accessToken,
    refreshToken,
    instanceUrl,
  });

  salesforceConnection.on('error', errorCallback);

  return salesforceConnection;
};

const getSalesforceConnection = async (environment) => {
  // Handle backward compatibility: if environment is a string, use default instance
  if (typeof environment === 'string') {
    return getSalesforceInstance(environment, 'default');
  }

  // Handle array of environments (existing functionality)
  if (Array.isArray(environment)) {
    /**
     * Some hoop jumping to get async awaits to work with reduce.
     * Since the accumulator is a promise it must be awaited every time
     */
    return environment.reduce(async (connections, instanceType) => {
      connections = await connections;
      connections[instanceType] = await getSalesforceInstance(instanceType, 'default');
      return connections;
    }, Promise.resolve({}));
  }

  // Handle object format for multiple instances: { instanceName: { environment, type } }
  if (typeof environment === 'object') {
    const connections = {};
    for (const [instanceName, config] of Object.entries(environment)) {
      if (typeof config === 'string') {
        // Simple format: { instanceName: 'environment' } - use default type
        connections[instanceName] = await getSalesforceInstance(config, 'default');
      } else if (typeof config === 'object' && config.environment) {
        // Extended format: { instanceName: { environment: 'dev', type: 'salesforce2' } }
        const instanceType = config.type || 'default';
        connections[instanceName] = await getSalesforceInstance(config.environment, instanceType);
      } else {
        throw new Error(`Invalid configuration for Salesforce instance '${instanceName}'. Expected string or object with 'environment' property.`);
      }
    }
    return connections;
  }

  throw new Error('Invalid environment parameter. Expected string, array, or object.');
};

module.exports = { getSalesforceConnection };
