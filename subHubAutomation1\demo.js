// Demo script to test salesforce2 connection
// Import Salesforce connections from Lambda Layer
// For local testing, use relative path; for Lambda, use /opt/nodejs/getConnections
let getConnections;
try {
  getConnections = require('/opt/nodejs/getConnections').getConnections;
} catch (error) {
  getConnections = require('../layers/connections/nodejs/getConnections').getConnections;
}

let initialized = false;
let salesforce, salesforceCT;

async function setUpConnections(environment) {
  if (!initialized) {
    console.log(`Initializing both Salesforce connections for environment: ${environment}`);

    const connections = await getConnections({
      salesforce: environment,   // Original Salesforce (salesforce-authentication)
      salesforceCT: environment   // New Salesforce (salesforce-two-authentication)
    });

    console.log('Raw connections object keys:', Object.keys(connections));
    console.log('salesforce type:', typeof connections.salesforce);
    console.log('salesforceCT type:', typeof connections.salesforceCT);

    salesforce = connections.salesforce;
    salesforceCT = connections.salesforceCT;
    initialized = true;

    console.log("Both Salesforce connections initialized");
    console.log(`salesforce available: ${!!salesforce}`);
    console.log(`salesforceCT available: ${!!salesforceCT}`);
  }
  return { salesforce, salesforceCT };
}

async function testSalesforceCTConnection() {
  console.log('Testing salesforceCT connection...');

  console.log('Debug salesforceCT object:');
  console.log('Type:', typeof salesforceCT);
  console.log('Is null/undefined:', salesforceCT == null);
  console.log('Has query method:', typeof salesforceCT?.query);
  console.log('Object keys:', salesforceCT ? Object.keys(salesforceCT) : 'N/A');

  if (!salesforceCT) {
    return { success: false, error: 'salesforceCT connection is null or undefined' };
  }

  if (typeof salesforceCT.query !== 'function') {
    return { success: false, error: 'salesforceCT.query is not a function' };
  }

  try {
    const opportunityId = '006WD00000GpEaoYAF';
    const soql = `
      SELECT 
        Id,
        Name,
        StageName,
        Amount,
        CloseDate,
        Account.Name,
        Owner.Name,
        CreatedDate,
        LastModifiedDate
      FROM Opportunity 
      WHERE Id = '${opportunityId}'
    `;

    console.log('SOQL Query:', soql);

    const result = await salesforceCT.query(soql);

    if (result.records && result.records.length > 0) {
      const opp = result.records[0];
      console.log('Retrieved Opportunity from salesforceCT:');
      console.log(`ID: ${opp.Id}`);
      console.log(`Name: ${opp.Name}`);
      console.log(`Stage: ${opp.StageName}`);
      console.log(`Amount: $${opp.Amount || 'N/A'}`);
      console.log(`Close Date: ${opp.CloseDate}`);
      console.log(`Account: ${opp.Account?.Name || 'N/A'}`);
      console.log(`Owner: ${opp.Owner?.Name || 'N/A'}`);
      console.log(`Created: ${new Date(opp.CreatedDate).toLocaleString()}`);
      console.log(`Modified: ${new Date(opp.LastModifiedDate).toLocaleString()}`);

      return { success: true, data: opp };
    } else {
      console.log('No Opportunity found with that ID');
      return { success: false, message: 'No records found' };
    }
  } catch (error) {
    console.error('Error testing salesforceCT connection:', error);
    return { success: false, error: error.message };
  }
}

async function testSalesforceConnection() {
  console.log('\nTesting salesforce connection (salesforce-authentication)...');

  console.log('Debug salesforce object:');
  console.log('Type:', typeof salesforce);
  console.log('Is null/undefined:', salesforce == null);
  console.log('Has query method:', typeof salesforce?.query);

  if (!salesforce) {
    return { success: false, error: 'salesforce connection is null or undefined' };
  }

  if (typeof salesforce.query !== 'function') {
    return { success: false, error: 'salesforce.query is not a function' };
  }

  try {
    const barrierId = 'a2dU8000000MmVrIAK';

    const soql = `
      SELECT
        Id,
        Name,
        Remediation_Status__c,
        RecordType.Name,
        Account__r.Name,
        Account__r.BillingAddress,
        Locations_of_Barrier__c,
        CreatedDate,
        LastModifiedDate
      FROM Barrier__c
      WHERE Id = '${barrierId}'
    `;

    console.log('SOQL Query:', soql);

    const result = await salesforce.query(soql);

    if (result.records && result.records.length > 0) {
      const barrier = result.records[0];
      console.log('Retrieved Barrier from salesforce:');
      console.log(`ID: ${barrier.Id}`);
      console.log(`Name: ${barrier.Name}`);
      console.log(`Status: ${barrier.Remediation_Status__c}`);
      console.log(`Record Type: ${barrier.RecordType?.Name || 'N/A'}`);
      console.log(`Account: ${barrier.Account__r?.Name || 'N/A'}`);
      console.log(`Location: ${barrier.Locations_of_Barrier__c || 'N/A'}`);
      console.log(`Created: ${new Date(barrier.CreatedDate).toLocaleString()}`);
      console.log(`Modified: ${new Date(barrier.LastModifiedDate).toLocaleString()}`);

      return { success: true, data: barrier };
    } else {
      console.log('No Barrier found with that ID');
      return { success: false, message: 'No records found' };
    }

  } catch (error) {
    console.error('Error testing salesforce connection:', error);
    return { success: false, error: error.message };
  }
}

async function runDemo() {
  console.log('🚀 Starting Both Salesforce Connections Demo');
  console.log('═══════════════════════════════════════════════');

  try {
    const environment = 'dev';
    console.log(`🌍 Environment: ${environment}`);

    await setUpConnections(environment);

    // Test both connections
    console.log('\n🧪 Testing both Salesforce connections...');

    // Test original salesforce connection
    console.log('\n📋 Test 1: Original Salesforce Connection');
    const salesforceResult = await testSalesforceConnection();
    if (salesforceResult.success) {
      console.log('✅ Original Salesforce - PASSED');
    } else {
      console.log('❌ Original Salesforce - FAILED:', salesforceResult.error || salesforceResult.message);
    }

    // Test salesforceCT connection
    console.log('\n📋 Test 2: SalesforceCT Connection');
    const salesforceCTResult = await testSalesforceCTConnection();
    if (salesforceCTResult.success) {
      console.log('✅ SalesforceCT - PASSED');
    } else {
      console.log('❌ SalesforceCT - FAILED:', salesforceCTResult.error || salesforceCTResult.message);
    }

    // Summary
    console.log('\n📊 Summary:');
    console.log(`   Original Salesforce: ${salesforceResult.success ? '✅ Working' : '❌ Failed'}`);
    console.log(`   SalesforceCT: ${salesforceCTResult.success ? '✅ Working' : '❌ Failed'}`);

    console.log('\n🎉 Demo completed!');

  } catch (error) {
    console.error('💥 Demo failed:', error);
  }
}

const lambdaHandler = async (event, context) => {
  try {
    const environment = context.invokedFunctionArn.endsWith('production') ? 'prod' : 'dev';
    console.log(`Environment: ${environment}`);

    await setUpConnections(environment);

    const result = await testSalesforceCTConnection();

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: 'SalesforceCT connection test completed',
        result: result
      })
    };
  } catch (error) {
    console.error('Error in demo Lambda:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({
        error: 'Demo failed',
        details: error.message
      })
    };
  }
};

module.exports = {
  runDemo,
  testSalesforceCTConnection,
  testSalesforceConnection,
  setUpConnections,
  lambdaHandler
};

if (require.main === module) {
  runDemo();
}
