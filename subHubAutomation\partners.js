/** Round-robin assignment */
const getNextPartner = (partners) => {
  if (!partners.length) return null;
  partners.sort((a, b) => a.total_job_count - b.total_job_count || a.oid.localeCompare(b.oid));
  partners[0].total_job_count += 1;
  return partners[0];
};

/** Map partners to barriers */
const mapPartnersToBarriers = (barriers, allPartners, regionMap, eventTypeMap) => {
  return barriers.map(b => {
    const eventType = eventTypeMap[b.recordType?.trim().toLowerCase()] || null;
    const region = regionMap[b.zipcode] || null;

    let available = allPartners.filter(p => p.event_type === eventType && p.region === region);
    if (!available.length) available = allPartners.filter(p => p.event_type === eventType);

    const assignedPartner = getNextPartner(available);

    return { ...b, eventType, regionId: region, availablePartners: available, assignedPartner };
  });
};

module.exports = { mapPartnersToBarriers };
