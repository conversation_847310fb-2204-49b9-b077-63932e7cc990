/** Round-robin assignment with global job count tracking */
const getNextPartner = (partners, globalPartnerMap) => {
  if (!partners.length) return null;

  // Sort by current job count (from global map) and then by oid for consistency
  partners.sort((a, b) => {
    const aCount = globalPartnerMap[a.oid]?.total_job_count || 0;
    const bCount = globalPartnerMap[b.oid]?.total_job_count || 0;
    return aCount - bCount || a.oid.localeCompare(b.oid);
  });

  const selectedPartner = partners[0];

  // Update the global job count
  if (globalPartnerMap[selectedPartner.oid]) {
    globalPartnerMap[selectedPartner.oid].total_job_count += 1;
  }

  // Return partner with updated count for display
  return {
    ...selectedPartner,
    total_job_count: globalPartnerMap[selectedPartner.oid]?.total_job_count || 0
  };
};

/** Map partners to barriers with proper round-robin tracking */
const mapPartnersToBarriers = (barriers, allPartners, regionMap, eventTypeMap) => {
  // Create a global partner map for tracking job counts across all assignments
  const globalPartnerMap = {};
  allPartners.forEach(p => {
    globalPartnerMap[p.oid] = { ...p };
  });

  return barriers.map(b => {
    const eventType = eventTypeMap[b.recordType?.trim().toLowerCase()] || null;
    const region = regionMap[b.zipcode] || null;

    // Find available partners for this barrier
    let available = allPartners.filter(p => p.event_type === eventType && p.region === region);
    if (!available.length) {
      available = allPartners.filter(p => p.event_type === eventType);
    }

    // Get available partners with current job counts for display
    const availableWithCurrentCounts = available.map(p => ({
      ...p,
      total_job_count: globalPartnerMap[p.oid]?.total_job_count || 0
    }));

    // Assign partner using global tracking
    const assignedPartner = getNextPartner(available, globalPartnerMap);

    return {
      ...b,
      eventType,
      regionId: region,
      availablePartners: availableWithCurrentCounts,
      assignedPartner
    };
  });
};

module.exports = { mapPartnersToBarriers };
