const { getConnections } = require('/opt/nodejs/getConnections');

let initialized = false;
let salesforce, scheduler2Db;

/** Initialize Salesforce + DB connections */
const setUpConnections = async (environment) => {
  if (!initialized) {
    ({ salesforce, scheduler2Db } = await getConnections({
      salesforce: environment,
      scheduler2Db: environment
    }));
    initialized = true;
    console.log("Connections initialized");
  }
  return { salesforce, scheduler2Db };
};

/** Accessors — always return the latest connections */
const getSalesforce = () => {
  if (!salesforce) throw new Error("Salesforce not initialized. Call setUpConnections() first.");
  return salesforce;
};

const getScheduler2Db = () => {
  if (!scheduler2Db) throw new Error("Scheduler2 DB not initialized. Call setUpConnections() first.");
  return scheduler2Db;
};

module.exports = {
  setUpConnections,
  getSalesforce,
  getScheduler2Db
};
