const { getSalesforce } = require('./connections');

/** Update Barrier__c status */
const markBarrierSentForInspection = async (barrierId) => {
  const salesforce = getSalesforce();

  const result = await salesforce.sobject("Barrier__c").update({
    Id: barrierId,
    Remediation_Status__c: "Sent for Inspection"
  });

  if (!result.success) {
    throw new Error(`Failed to update Barrier__c ${barrierId}: ${result.errors.join(", ")}`);
  }

  console.log(`Barrier__c ${barrierId} updated to Sent for Inspection`);
  return result;
};

module.exports = { markBarrierSentForInspection };
