# SubHub Automation Framework

Lambda Layers being utilized:
connectionLayer
sendEmailLayer



## How it works ?


Lambda Triggered

Initializes Salesforce + DB connections.

Fetches up to 50 barriers from Salesforce.

Extracts unique zipcodes and record types.

Gets regions for zipcodes from DB.

Gets event types for record types from DB.

Fetches partners by event types and regions.

Maps partners to each barrier (region first, fallback event type).

Returns response with barrier count + sample data.


Update



<!-- List of All Queries Including SOQL -->


## 1 Fetch Barriers from Salesforce (SOQL)

SELECT
    Id,
    RecordType.Name,
    Account__r.BillingAddress,
    Locations_of_Barrier__c,
    Account__c,
    Account__r.Name
FROM Barrier__c
WHERE Remediation_Status__c = 'Ready for Subhub AutoCreate'
ORDER BY LastModifiedDate DESC
LIMIT {maxRecords};


## 2 Map Record Types

SELECT event_type, name
FROM event_types
WHERE LOWER(TRIM(name)) IN ({normalizedRecordTypes});


## 3 Map Zipcodes → Regions

SELECT zip_code, region
FROM region_zipcodes
WHERE zip_code IN ({zipcodes});




## 4 Fetch Partners for Event Types & Regions (Including Multi-Region Partners)

WITH partners_with_regions AS (
  -- Get partners with their primary region
  SELECT
      u.oid,
      (u.firstname || ' ' || u.lastname) AS partner_name,
      u.region as primary_region,
      et.event_type,
      u.region as available_region
  FROM users u
  JOIN user_roles ur ON u.oid = ur.oid
  JOIN users_eventtypes uet ON u.oid = uet.oid
  JOIN event_types et ON uet.type_id = et.id
  WHERE u.active = TRUE
    AND ur.role_id = {roleId}
    AND ur.department_id = 8
    AND et.event_type IN ({eventTypes})

  UNION

  -- Get partners with their additional regions from user_regions table
  SELECT
      u.oid,
      (u.firstname || ' ' || u.lastname) AS partner_name,
      u.region as primary_region,
      et.event_type,
      ur_regions.region_id as available_region
  FROM users u
  JOIN user_roles ur ON u.oid = ur.oid
  JOIN users_eventtypes uet ON u.oid = uet.oid
  JOIN event_types et ON uet.type_id = et.id
  JOIN user_regions ur_regions ON u.oid = ur_regions.oid
  WHERE u.active = TRUE
    AND ur.role_id = {roleId}
    AND ur.department_id = 8
    AND et.event_type IN ({eventTypes})
)
SELECT oid, partner_name, primary_region, event_type, available_region
FROM partners_with_regions
WHERE available_region IN ({regions}) -- filters by all available regions
ORDER BY primary_region, oid;



## 5 Get Active Job Counts per Partner

SELECT 
    eo.oid,
    COUNT(pe.id) AS total_jobs
FROM partner.event_oids eo
JOIN partner.event pe
    ON eo.event_id = pe.id
WHERE eo.oid IN ({partnerOids})
  AND pe.status IN (
    '2nd Attempt to Schedule',
    'Remediation Scheduled',
    'Customer Unresponsive',
    'Customer Reschedule Request',
    'Sent to Partner',
    'Scheduling Remediation',
    'Ready for Subhub AutoCreate',
    'Contractor Revision Needed',
    'Insp Completed - Need Estimate'
  )
GROUP BY eo.oid;



## 6 Create Partner Event

INSERT INTO partner.event (
    type,
    status,
    account_id,
    account_name,
    num_unit,
    sf_ids,
    scheduled_date,
    last_modified,
    address
) VALUES (
    {eventType},
    'Scheduled',
    {accountId},
    {accountName},
    {num_unit},
    {sf_ids},   
    NOW(),
    NOW(),
    {formattedAddress}
)
RETURNING id;


##  7 Assign Partner to Event

INSERT INTO partner.event_oids (event_id, oid)
VALUES ({eventId}, {partnerOid});



## 8 Update Barrier Status in Salesforce


UPDATE Barrier__c
SET Remediation_Status__c = 'Sent for Inspection'
WHERE Id = {barrierId};

