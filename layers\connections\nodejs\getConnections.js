const { getDatabaseConnection } = require('./src/getDatabaseConnection');
const { getSalesforceConnection } = require('./src/getSalesforceConnection');
const { getUplightConnection } = require('./src/getUplightConnection');

/* eslint-disable import/no-commonjs */
const getConnections = async (connectionTypes) => {
  try {
    const {
      scheduler1Db,
      scheduler2Db,
      salesforce,
      salesforceCT, // New: Support for second Salesforce instance
      nationalGrid,
      eversource,
    } = connectionTypes;

    const connections = {};
    if (scheduler1Db)
      connections.scheduler1Db = await getDatabaseConnection({ scheduler1Db });
    if (scheduler2Db)
      connections.scheduler2Db = await getDatabaseConnection({ scheduler2Db });
    if (salesforce)
      connections.salesforce = await getSalesforceConnection(salesforce);
    if (salesforceCT) {
      const sfCTConnection = await getSalesforceConnection({
        salesforceCT: { environment: salesforceCT, type: 'salesforceCT' }
      });
      connections.salesforceCT = sfCTConnection.salesforceCT; // Extract the actual connection
    }
    if (nationalGrid)
      connections.nationalGrid = await getUplightConnection({ nationalGrid });
    if (eversource)
      connections.eversource = await getUplightConnection({ eversource });

    return connections;
  } catch (error) {
    return console.error(error, error.stack);
  }
};

module.exports = {
  getConnections,
};
