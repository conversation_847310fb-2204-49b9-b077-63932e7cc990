// const { sendEmail } = require('/opt/nodejs/sendEmail');
const { setUpConnections } = require('./connections');
const { fetchBarriers, processBarriers } = require('./barriers');
const { getRegionForZipcodes, getEventTypeForRecordType, fetchPartners } = require('./db');
const { mapPartnersToBarriers } = require('./partners');


exports.handler = async (event,context) => {
  try {
    // const env = process.env.SF_ENV || 'dev';
    const env = 'dev';
    // const env = 'prod';
    const maxRecords = Number(process.env.SF_LIMIT || 2);

    console.log("Env", env);

    await setUpConnections(env);

    // Step 1: Fetch barriers
    const barriers = await fetchBarriers({ maxRecords });
    console.log("barriers:", barriers);
    if (!barriers.length) {
      return { statusCode: 200, body: JSON.stringify({ message: 'No barriers found' }) };
    }

    // Step 2: Prepare lookup maps
    const uniqueZipcodes = [...new Set(barriers.map(b => b.zipcode).filter(Boolean))];
    console.log("uniqueZipcodes:", uniqueZipcodes); // 01109
    const uniqueRecordTypes = [...new Set(barriers.map(b => b.recordType).filter(Boolean))];
    console.log("uniqueRecordTypes:", uniqueRecordTypes); // K&T

    const regionMap = await getRegionForZipcodes(uniqueZipcodes);
    console.log("regionMap:", regionMap);
    const eventTypeMap = await getEventTypeForRecordType(uniqueRecordTypes);
    console.log("eventTypeMap:", eventTypeMap);

    // Step 3: Fetch all partners
    const eventTypes = [...new Set(Object.values(eventTypeMap).filter(Boolean))];
    console.log("eventTypes:", eventTypes);
    const regions = [...new Set(Object.values(regionMap).filter(Boolean))];
    console.log("regions:", regions);
    const allPartners = await fetchPartners(eventTypes, regions);
    console.log("allPartners:", allPartners);

    // Step 4: Assign partners to barriers
    const enrichedBarriers = mapPartnersToBarriers(barriers, allPartners, regionMap, eventTypeMap);
    console.log("enrichedBarriers:", enrichedBarriers);

    // Step 5: Process barriers (create events, assign partners, update Salesforce)
    const processResults = await processBarriers(enrichedBarriers);
    console.log("processResults:", processResults);

    console.log("Processed barriers>>>", JSON.stringify(processResults, null, 2));

    /* For Alerting to Dev Team */

    const failedResults = processResults.filter(r => r.status === 'failed');

    if (failedResults.length > 0) {
      const bodyText = failedResults.map(f => {
        if (f.error) {
          return [
            `BarrierId: ${f.barrierId}`,
            `Status: ${f.status}`,
            `Error: ${f.error}`
          ].join("\n");
        } else {
          return [
            `BarrierId: ${f.barrierId}`,
            `Status: ${f.status}`,
            `Reason: ${f.reason}`
          ].join("\n");
        }
      }).join("\n\n-------------------------\n\n");

      // await sendEmail({
      //   subject: `Failed to process ${failedResults.length} out of ${processResults.length}`,
      //   emails: ["<EMAIL>", "<EMAIL>"],
      //   error: bodyText
      // });
    }

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: `Processed ${enrichedBarriers.length} barriers`,
        successCount: processResults.filter(r => r.status === 'success').length,
        failureCount: processResults.filter(r => r.status === 'failed').length,
        partnerAssignments: enrichedBarriers,
        processResults
      })
    };
  } catch (err) {
    return { statusCode: 500, body: JSON.stringify({ error: String(err) }) };
  }
};
