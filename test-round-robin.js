const { mapPartnersToBarriers } = require('./subHubAutomation/partners');

// Test data
const mockPartners = [
  { oid: 'partner1', partner_name: 'Partner A', region: 100, event_type: '008800', total_job_count: 5 },
  { oid: 'partner2', partner_name: 'Partner B', region: 100, event_type: '008800', total_job_count: 2 },
  { oid: 'partner3', partner_name: 'Partner C', region: 100, event_type: '008800', total_job_count: 8 }
];

const mockBarriers = [
  { barrierId: 'barrier1', recordType: 'K&T', zipcode: '01701' },
  { barrierId: 'barrier2', recordType: 'K&T', zipcode: '01701' },
  { barrierId: 'barrier3', recordType: 'K&T', zipcode: '01701' },
  { barrierId: 'barrier4', recordType: 'K&T', zipcode: '01701' }
];

const regionMap = { '01701': 100 };
const eventTypeMap = { 'k&t': '008800' };

console.log('=== TESTING ROUND-ROBIN ASSIGNMENT ===\n');

console.log('Initial Partner Job Counts:');
mockPartners.forEach(p => {
  console.log(`${p.partner_name}: ${p.total_job_count} jobs`);
});

console.log('\n=== Assignment Results ===');

const results = mapPartnersToBarriers(mockBarriers, mockPartners, regionMap, eventTypeMap);

results.forEach((result, index) => {
  console.log(`\nBarrier ${index + 1} (${result.barrierId}):`);
  console.log(`  Assigned to: ${result.assignedPartner?.partner_name || 'None'}`);
  console.log(`  Partner job count after assignment: ${result.assignedPartner?.total_job_count || 0}`);
  
  console.log('  Available partners with current counts:');
  result.availablePartners.forEach(p => {
    console.log(`    ${p.partner_name}: ${p.total_job_count} jobs`);
  });
});

console.log('\n=== Expected Behavior ===');
console.log('1. First assignment should go to Partner B (lowest count: 2)');
console.log('2. Second assignment should go to Partner B again (now 3, still lowest)');
console.log('3. Third assignment should go to Partner B again (now 4, still lowest)');
console.log('4. Fourth assignment should go to Partner A (5 jobs, now tied with Partner B at 4)');
