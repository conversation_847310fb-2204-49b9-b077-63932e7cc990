AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: >
  subhub-automation

Parameters:
  Environment:
    Type: String
    Default: dev
    AllowedValues: [dev, staging, prod]
    Description: Environment for Salesforce connection

  MaxRecords:
    Type: Number
    Default: 10
    MinValue: 1
    MaxValue: 10
    Description: Maximum number of barriers to process per run

Globals:
  Function:
    Timeout: 300

Resources:
  SubhubAutomationFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: subHubAutomation/
      Handler: index.handler
      Runtime: nodejs20.x
      Architectures:
        - x86_64
      Layers:
        - arn:aws:lambda:us-east-1:213998484574:layer:connectionLayer:25
      Environment:
        Variables:
          SF_ENV: !Ref Environment
          SF_LIMIT: !Ref MaxRecords
      Policies:
        - S3ReadPolicy:
            BucketName: salesforce-authentication
        - KMSDecryptPolicy:
            KeyId: "*"
        - Version: "2012-10-17"
          Statement:
            - Effect: Allow
              Action:
                - lambda:GetLayerVersion
              Resource:
                - "arn:aws:lambda:us-east-1:213998484574:layer:connectionLayer:25"
            - Effect: Allow
              Action:
                - rds:DescribeDBInstances
                - rds-db:connect
              Resource: "*"
      Events:
        DailySchedule:
          Type: Schedule
          Properties:
            Schedule: cron(0 11 * * ? *)  # Run daily at 11 AM UTC
            Description: "Daily Subhub automation trigger"
            Enabled: true



Outputs:
  SubhubAutomationFunction:
    Description: "Subhub Automation Lambda Function ARN"
    Value: !GetAtt SubhubAutomationFunction.Arn
  SubhubAutomationFunctionIamRole:
    Description: "Implicit IAM Role created for Subhub Automation function"
    Value: !GetAtt SubhubAutomationFunctionRole.Arn
