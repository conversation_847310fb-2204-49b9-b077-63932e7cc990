const { getSalesforce, getScheduler2Db } = require('./connections');
const { markBarrierSentForInspection } = require('./salesforce');

/** Fetch barriers from Salesforce */
const fetchBarriers = async ({ maxRecords = 7 }) => {
  const salesforce = getSalesforce();

  const status = "Ready for Subhub AutoCreate";
  const limit = Math.min(maxRecords, 10);

  const soql = `
  SELECT Id,
         RecordType.Name,
         Account__r.BillingAddress,
         Number_of_Units_Affected__c,
         Locations_of_Barrier__c,
         Account__c,
         Account__r.Name,
         Account__r.Site_Id_NS__c
  FROM Barrier__c
  WHERE Remediation_Status__c = '${status}'
  ORDER BY LastModifiedDate DESC
  LIMIT ${limit}
`;

  const result = await salesforce.query(soql);
  console.log("Founded barriers>>>", JSON.stringify(result, null, 2));

  return result.records.map(mapBarrierRecord);
};

/** Normalize Barrier record */
const mapBarrierRecord = (r) => {
  const account = r.Account__r || {};
  const billing = account.BillingAddress || {};
  const rawLocation = r.Locations_of_Barrier__c || "";
  const numberOfUnits = r.Number_of_Units_Affected__c;
  let zipcode = billing.postalCode || null;
  if (!zipcode && rawLocation) {
    const match = rawLocation.match(/\b\d{5}(?:-\d{4})?\b/);
    zipcode = match ? match[0] : null;
  }

  console.log("Site_ID is", account.Site_Id_NS__c, numberOfUnits);
  return {
    barrierId: r.Id,
    recordType: r.RecordType?.Name || null,
    accountId: r.Account__c,
    accountName: account.Name || null,
    siteId: account.Site_Id_NS__c,
    numberOfUnits:r.Number_of_Units_Affected__c,
    rawLocation,
    zipcode,
    billingAddress: [billing.street, billing.city, billing.state, billing.postalCode, billing.country]
      .filter(Boolean).join(', ') || null,
    billingAddressObj: billing
  };
};

/** Create partner event in DB */
const createPartnerEvent = async (barrier) => {
  const scheduler2Db = getScheduler2Db();

  const formattedAddress = barrier.billingAddress
    ? barrier.billingAddress.toLowerCase().replace(/,/g, '').replace(/\s+/g, ' ').trim()
    : null;

  const todayDate = new Date().toISOString().slice(0, 10);

  const [insertedEvent] = await scheduler2Db('partner.event')
    .insert({
      type: barrier.eventType,
      status: 'Sent for Inspection',
      account_id: barrier.accountId,
      account_name: barrier.accountName,
      num_unit: !barrier.numberOfUnits ? 1 : barrier.numberOfUnits,
      sf_ids: JSON.stringify({ barrier_id: barrier.barrierId, account_id: barrier.accountId, site_id: barrier.siteId }),
      last_modified: new Date(),
      date_sent_to_partner: todayDate,
      address: formattedAddress,
      scheduled_by: 'SubHub_Automation',
      site_id: barrier.siteId
    })
    .returning(['id', 'account_id', 'date_sent_to_partner']);

  return insertedEvent.id;
};

/** Assign partner to event */
const assignPartnerToEvent = async (eventId, partnerOid) => {
  const scheduler2Db = getScheduler2Db();
  await scheduler2Db('partner.event_oids').insert({ event_id: eventId, oid: partnerOid });
};

/** Process all barriers */
const processBarriers = async (barriers) => {
  const results = [];

  for (const b of barriers) {
    if (!b.assignedPartner) {
      results.push({ barrierId: b.barrierId, status: 'failed', reason: 'No partner available' });
      continue;
    }

    try {
      const eventId = await createPartnerEvent(b);
      await assignPartnerToEvent(eventId, b.assignedPartner.oid);
      await markBarrierSentForInspection(b.barrierId);

      results.push({
        barrierId: b.barrierId,
        eventId,
        partnerId: b.assignedPartner.oid,
        partnerName: b.assignedPartner.partner_name,
        status: 'success'
      });
    } catch (err) {
      console.error(`Failed to process barrier ${b.barrierId}:`, err);
      results.push({ barrierId: b.barrierId, status: 'failed', error: err.message });
    }
  }

  return results;
};

module.exports = { fetchBarriers, processBarriers };
