const { getScheduler2Db } = require('./connections');

/** Get event types mapped by record types */
const getEventTypeForRecordType = async (recordTypes) => {
  const scheduler2Db = getScheduler2Db();

  const normalized = recordTypes.map(r => r.trim().toLowerCase());
  const rows = await scheduler2Db("event_types")
    .select("event_type", "name")
    .whereIn(scheduler2Db.raw('LOWER(TRIM(name))'), normalized);

  const map = {};
  rows.forEach(r => { map[r.name.trim().toLowerCase()] = r.event_type; });
  return map;
};

/** Get regions mapped by zipcodes */
const getRegionForZipcodes = async (zipcodes) => {
  const scheduler2Db = getScheduler2Db();

  const results = await scheduler2Db('region_zipcodes')
    .select('zip_code', 'region')
    .whereIn('zip_code', zipcodes)
    .where('department_id', 1);

  const map = {};
  results.forEach(r => { map[r.zip_code] = r.region; });
  return map;
};

/** Fetch partners with job counts - includes partners working in multiple regions */
const fetchPartners = async (eventTypes, regions = [], roleId = 6) => {
  const scheduler2Db = getScheduler2Db();

  // Single optimized query using CTE and LEFT JOIN for job counts
  const query = scheduler2Db.raw(`
    WITH partners_with_regions AS (
      -- Get partners with their primary region
      SELECT
        u.oid,
        (u.firstname || ' ' || u.lastname) AS partner_name,
        u.region,
        et.event_type
      FROM users u
      JOIN user_roles ur ON u.oid = ur.oid
      JOIN users_eventtypes uet ON u.oid = uet.oid
      JOIN event_types et ON uet.type_id = et.id
      WHERE u.active = true
        AND ur.role_id = ?
        AND ur.department_id = 8
        AND et.event_type = ANY(?)
        ${regions.length ? 'AND u.region = ANY(?)' : ''}

      UNION

      -- Get partners with their additional regions from user_regions table
      SELECT
        u.oid,
        (u.firstname || ' ' || u.lastname) AS partner_name,
        ur_regions.region_id as region,
        et.event_type
      FROM users u
      JOIN user_roles ur ON u.oid = ur.oid
      JOIN users_eventtypes uet ON u.oid = uet.oid
      JOIN event_types et ON uet.type_id = et.id
      JOIN user_regions ur_regions ON u.oid = ur_regions.oid
      WHERE u.active = true
        AND ur.role_id = ?
        AND ur.department_id = 8
        AND et.event_type = ANY(?)
        ${regions.length ? 'AND ur_regions.region_id = ANY(?)' : ''}
    ),
    job_counts AS (
      SELECT
        eo.oid,
        COUNT(pe.id) as total_jobs
      FROM partner.event_oids eo
      JOIN partner.event pe ON eo.event_id = pe.id
      WHERE pe.status IN (
        '2nd Attempt to Schedule',
        'Remediation Scheduled',
        'Customer Unresponsive',
        'Customer Reschedule Request',
        'Sent to Partner',
        'Scheduling Remediation',
        'Ready for Subhub AutoCreate',
        'Contractor Revision Needed',
        'Insp Completed - Need Estimate'
      )
      GROUP BY eo.oid
    )
    SELECT
      pwr.oid,
      pwr.partner_name,
      pwr.region,
      pwr.event_type,
      COALESCE(jc.total_jobs, 0) as total_job_count
    FROM partners_with_regions pwr
    LEFT JOIN job_counts jc ON pwr.oid = jc.oid
    ORDER BY pwr.region, pwr.oid
  `, regions.length ?
    [roleId, eventTypes, regions, roleId, eventTypes, regions] :
    [roleId, eventTypes, roleId, eventTypes]
  );

  const result = await query;
  return result.rows;
};

module.exports = { getEventTypeForRecordType, getRegionForZipcodes, fetchPartners };
