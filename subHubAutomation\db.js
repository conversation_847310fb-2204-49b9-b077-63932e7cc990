const { getScheduler2Db } = require('./connections');

/** Get event types mapped by record types */
const getEventTypeForRecordType = async (recordTypes) => {
  const scheduler2Db = getScheduler2Db();

  const normalized = recordTypes.map(r => r.trim().toLowerCase());
  const rows = await scheduler2Db("event_types")
    .select("event_type", "name")
    .whereIn(scheduler2Db.raw('LOWER(TRIM(name))'), normalized);

  const map = {};
  rows.forEach(r => { map[r.name.trim().toLowerCase()] = r.event_type; });
  return map;
};

/** Get regions mapped by zipcodes */
const getRegionForZipcodes = async (zipcodes) => {
  const scheduler2Db = getScheduler2Db();

  const results = await scheduler2Db('region_zipcodes')
    .select('zip_code', 'region')
    .whereIn('zip_code', zipcodes)
    .where('department_id', 1);

  const map = {};
  results.forEach(r => { map[r.zip_code] = r.region; });
  return map;
};

/** Fetch partners with job counts - includes partners working in multiple regions */
const fetchPartners = async (eventTypes, regions = [], roleId = 6) => {
  const scheduler2Db = getScheduler2Db();

  // Step 1: Get partners with primary regions (original working query)
  let baseQuery = scheduler2Db('users as u')
    .select(
      'u.oid',
      scheduler2Db.raw("(u.firstname || ' ' || u.lastname) AS partner_name"),
      'u.region',
      'et.event_type'
    )
    .join('user_roles as ur', 'u.oid', 'ur.oid')
    .join('users_eventtypes as uet', 'u.oid', 'uet.oid')
    .join('event_types as et', 'uet.type_id', 'et.id')
    .where('u.active', true)
    .where('ur.role_id', roleId)
    .where('ur.department_id', 8)
    .whereIn('et.event_type', eventTypes)
    .orderBy('u.region')
    .orderBy('u.oid');

  // Apply region filter if specified
  if (regions.length) {
    baseQuery = baseQuery.where(function() {
      this.whereIn('u.region', regions)
        .orWhereExists(function() {
          this.select('*')
            .from('user_regions as ur_multi')
            .whereRaw('ur_multi.oid = u.oid')
            .whereIn('ur_multi.region_id', regions);
        });
    });
  }

  const partners = await baseQuery;

  // Step 2: For partners that work in multiple regions, create additional entries
  if (regions.length) {
    const additionalPartners = await scheduler2Db('users as u')
      .select(
        'u.oid',
        scheduler2Db.raw("(u.firstname || ' ' || u.lastname) AS partner_name"),
        'ur_multi.region_id as region',
        'et.event_type'
      )
      .join('user_roles as ur', 'u.oid', 'ur.oid')
      .join('users_eventtypes as uet', 'u.oid', 'uet.oid')
      .join('event_types as et', 'uet.type_id', 'et.id')
      .join('user_regions as ur_multi', 'u.oid', 'ur_multi.oid')
      .where('u.active', true)
      .where('ur.role_id', roleId)
      .where('ur.department_id', 8)
      .whereIn('et.event_type', eventTypes)
      .whereIn('ur_multi.region_id', regions)
      .whereRaw('ur_multi.region_id != u.region') // Only additional regions, not primary
      .orderBy('u.region')
      .orderBy('u.oid');

    // Combine both sets of partners
    partners.push(...additionalPartners);
  }

  // Attach job counts
  const partnerOids = partners.map(p => p.oid);
  const jobCounts = await scheduler2Db('partner.event_oids as eo')
    .select('eo.oid', scheduler2Db.raw('COUNT(pe.id) as total_jobs'))
    .join('partner.event as pe', 'eo.event_id', 'pe.id')
    .whereIn('eo.oid', partnerOids)
    .whereIn('pe.status', [
      '2nd Attempt to Schedule',
      'Remediation Scheduled',
      'Customer Unresponsive',
      'Customer Reschedule Request',
      'Sent to Partner',
      'Scheduling Remediation',
      'Ready for Subhub AutoCreate',
      'Contractor Revision Needed',
      'Insp Completed - Need Estimate'
    ])
    .groupBy('eo.oid');

  const jobCountMap = {};
  jobCounts.forEach(row => { jobCountMap[row.oid] = parseInt(row.total_jobs); });

  return partners.map(p => ({ ...p, total_job_count: jobCountMap[p.oid] || 0 }));
};

module.exports = { getEventTypeForRecordType, getRegionForZipcodes, fetchPartners };
