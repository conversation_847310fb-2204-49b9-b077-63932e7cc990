// Import Salesforce + DB connections from Lambda Layer
// For testing Locally
// const { getConnections } = await import("/opt/nodejs/getConnections.js").then(m => m);

const { getConnections } = require('/opt/nodejs/getConnections');


let initialized = false;
let salesforce, scheduler2Db;

/** Initialize connections */
async function setUpConnections(environment) {
  if (!initialized) {
    ({ salesforce, scheduler2Db } = await getConnections({
      salesforce: environment,
      scheduler2Db: environment
    }));
    initialized = true;
    console.log("Connections initialized");
  }
  return { salesforce, scheduler2Db };
}

/** Fetch Barrier__c records */
async function fetchBarriers({ maxRecords = 3 }) {
  const status = "Ready for Subhub AutoCreate";
  const safeStatus = status.replace(/'/g, "\\'");

  const soql = `
    SELECT
      Id,
      RecordType.Name,
      Account__r.BillingAddress,
      Locations_of_Barrier__c,
      Account__c,
      Account__r.Name
    FROM Barrier__c
    WHERE Remediation_Status__c = '${safeStatus}'
    ORDER BY LastModifiedDate DESC
    LIMIT ${Math.min(maxRecords, 3)}
  `;

  console.log("SOQL Query:", soql);
  const result = await salesforce.query(soql);

  return result.records.map(r => {
    const account = r.Account__r || {};
    const rawLocation = r.Locations_of_Barrier__c || "";
    const billingAddressObj = account.BillingAddress || null;

    let zipcode = null;
    let formattedBillingAddress = null;

    if (billingAddressObj && typeof billingAddressObj === 'object') {
      zipcode = billingAddressObj.postalCode || null;

      const addressParts = [
        billingAddressObj.street,
        billingAddressObj.city,
        billingAddressObj.state,
        billingAddressObj.postalCode,
        billingAddressObj.country
      ].filter(Boolean);

      formattedBillingAddress = addressParts.join(', ');
    }

    // fallback from rawLocation
    if (!zipcode && rawLocation) {
      const match = rawLocation.match(/\b\d{5}(?:-\d{4})?\b/);
      zipcode = match ? match[0] : null;
    }

    return {
      barrierId: r.Id,
      recordType: r.RecordType?.Name || null,
      accountId: r.Account__c,
      accountName: account.Name || null,
      rawLocation,
      zipcode,
      // numberOfUnits: r.Number_of_Units_Affected__c || 1,
      billingAddress: formattedBillingAddress,
      billingAddressObj
    };
  });
}

/** Map record types → event types (normalize strings) */
async function getEventTypeForRecordType(recordTypes) {
  const normalizedRecordTypes = recordTypes.map(r => r.trim().toLowerCase());
  const rows = await scheduler2Db("event_types")
    .select("event_type", "name")
    .whereIn(scheduler2Db.raw('LOWER(TRIM(name))'), normalizedRecordTypes);

  const map = {};
  rows.forEach(r => map[r.name.trim().toLowerCase()] = r.event_type);
  return map;
}

/** Map zipcodes → regions */
async function getRegionForZipcodes(zipcodes) {
  const results = await scheduler2Db('region_zipcodes')
    .select('zip_code', 'region')
    .whereIn('zip_code', zipcodes)
    .where('department_id', 1); // Filter by department_id = 1

  const map = {};
  results.forEach(r => map[r.zip_code] = r.region);
  return map;
}

/** Fetch partners for event types and regions */
async function fetchPartners(eventTypes, regions = [], roleId = 6) {
  let query = scheduler2Db('users as u')
    .select(
      'u.oid',
      scheduler2Db.raw("(u.firstname || ' ' || u.lastname) AS partner_name"),
      'u.region',
      'et.event_type'
    )
    .join('user_roles as ur', 'u.oid', 'ur.oid')
    .join('users_eventtypes as uet', 'u.oid', 'uet.oid')
    .join('event_types as et', 'uet.type_id', 'et.id')
    .where('u.active', true)
    .where('ur.role_id', roleId)
    .where('ur.department_id', 8)
    .whereIn('et.event_type', eventTypes)
    .orderBy('u.region')
    .orderBy('u.oid');

  if (regions.length) query.whereIn('u.region', regions);

  const partners = await query;

  // Fetch job counts
  const partnerOids = partners.map(p => p.oid);
  const activeStatuses = [
  '2nd Attempt to Schedule',
  'Remediation Scheduled',
  'Customer Unresponsive',
  'Customer Reschedule Request',
  'Sent to Partner',
  'Scheduling Remediation',
  'Ready for Subhub AutoCreate',
  'Contractor Revision Needed',
  'Insp Completed - Need Estimate'
];

// Get job counts for partners, only counting active jobs
const jobCounts = await scheduler2Db('partner.event_oids as eo')
  .select('eo.oid', scheduler2Db.raw('COUNT(pe.id) as total_jobs'))
  .join('partner.event as pe', 'eo.event_id', 'pe.id')
  .whereIn('eo.oid', partnerOids)
  .whereIn('pe.status', activeStatuses) 
  .groupBy('eo.oid');

  const jobCountMap = {};
  jobCounts.forEach(row => jobCountMap[row.oid] = parseInt(row.total_jobs));

  return partners.map(p => ({
    ...p,
    total_job_count: jobCountMap[p.oid] || 0
  }));
}

/** Round-robin assignment */
function getNextPartner(partners) {
  if (!partners.length) return null;
  partners.sort((a, b) => a.total_job_count - b.total_job_count || a.oid.localeCompare(b.oid));
  partners[0].total_job_count += 1;
  return partners[0];
}

/** Map partners to barriers */
function mapPartnersToBarriers(barriers, allPartners, regionMap, eventTypeMap) {
  return barriers.map(b => {
    const eventType = eventTypeMap[b.recordType?.trim().toLowerCase()] || null;
    const region = regionMap[b.zipcode] || null;

    let availablePartners = allPartners.filter(p => p.event_type === eventType && p.region === region);
    if (!availablePartners.length) {
      availablePartners = allPartners.filter(p => p.event_type === eventType);
    }

    const assignedPartner = getNextPartner(availablePartners);

    return {
      ...b,
      eventType,
      regionId: region,
      availablePartners,
      assignedPartner
    };
  });
}

/** Create partner event */
async function createPartnerEvent(barrier, assignedPartner) {
  const formattedAddress = barrier.billingAddress
    ? barrier.billingAddress.toLowerCase().replace(/,/g, '').replace(/\s+/g, ' ').trim()
    : null;

  const [eventId] = await scheduler2Db('partner.event')
    .insert({
      type: barrier.eventType,
      status: 'Scheduled',
      account_id: barrier.accountId,
      account_name: barrier.accountName,
      num_unit: barrier.numberOfUnits,
      sf_ids: JSON.stringify({ barrier_id: barrier.barrierId }),
      scheduled_date: new Date(),
      last_modified: new Date(),
      address: formattedAddress
    })
    .returning('id');

  return eventId;
}

/** Assign partner to event */
async function assignPartnerToEvent(eventId, partnerOid) {
  await scheduler2Db('partner.event_oids').insert({ event_id: eventId, oid: partnerOid });
}

/** Process barriers */
async function processBarriers(barriers) {
  const results = [];

  for (const b of barriers) {
    if (!b.assignedPartner) {
      results.push({ barrierId: b.barrierId, status: 'failed', reason: 'No partner available' });
      continue;
    }
    try {
      // Step 1: Create partner event
      const eventId = await createPartnerEvent(b, b.assignedPartner);
      console.log(`Created partner event ${eventId} for barrier ${b.barrierId}`);

      // Step 2: Assign partner to event
      await assignPartnerToEvent(eventId, b.assignedPartner.oid);
      console.log(`Assigned partner ${b.assignedPartner.oid} to event ${eventId}`);

      // Step 3: Update Salesforce barrier status (only after successful job creation)
      const salesforceResult = await markBarrierSentForInspection(b.barrierId);
      console.log(`Updated barrier ${b.barrierId} status to "Sent for Inspection"`);

      results.push({
        barrierId: b.barrierId,
        eventId,
        partnerId: b.assignedPartner.oid,
        partnerName: b.assignedPartner.partner_name,
        status: 'success',
        salesforceUpdate: salesforceResult
      });
    } catch (err) { 
      // TODO: Send email notification to Dev Team
    //   await sendEmail({
    //   subject: `${environment === 'dev' ? `DEV ${department}: ` : `${department}: `}Failed for processing barrier ${b.barrierId}`,
    //   emails: emailInfo.destinationIfError,
    //   err
    // });
      console.error(`Failed to process barrier ${b.barrierId}:`, err);
      results.push({ barrierId: b.barrierId, status: 'failed', error: err.message });
    }
  }

  return results;
}

/** Update a Barrier__c record status in Salesforce
 *
 * Only Executes when Job has been created in Scheduler 2 DB
*/
async function markBarrierSentForInspection(barrierId) {
  try {
    const result = await salesforce.sobject("Barrier__c").update({
      Id: barrierId,
      Remediation_Status__c: "Sent for Inspection"
    });

    if (!result.success) {
      console.error(`Failed to update Barrier__c ${barrierId}`, result.errors);
      throw new Error(`Update failed: ${result.errors.join(", ")}`);
    }

    console.log(`Barrier__c ${barrierId} updated to Sent for Inspection`);
    return result;
  } catch (err) {
    console.error(`Error updating Barrier__c ${barrierId}:`, err);
    throw err;
  }
}




/** Full Lambda Handler */
const lambdaHandler = async (event, context) => {
  try {
    // Use same environment detection as other working functions
    const environment = context.invokedFunctionArn.endsWith('production') ? 'prod' : 'dev';
    const maxRecords = Number(process.env.SF_LIMIT || 3);

    console.log(`Environment: ${environment}`);
    await setUpConnections(environment);

    const barriers = await fetchBarriers({ maxRecords });
    if (!barriers.length) {
      return { statusCode: 200, body: JSON.stringify({ message: 'No barriers found' }) };
    }

    const uniqueZipcodes = [...new Set(barriers.map(b => b.zipcode).filter(Boolean))];
    const uniqueRecordTypes = [...new Set(barriers.map(b => b.recordType).filter(Boolean))];

    const regionMap = await getRegionForZipcodes(uniqueZipcodes);
    const eventTypeMap = await getEventTypeForRecordType(uniqueRecordTypes);

    const eventTypes = [...new Set(Object.values(eventTypeMap).filter(Boolean))];
    const regions = [...new Set(Object.values(regionMap).filter(Boolean))];

    const allPartners = await fetchPartners(eventTypes, regions);

    const enrichedBarriers = mapPartnersToBarriers(barriers, allPartners, regionMap, eventTypeMap);

    // Process barriers: create events, assign partners, and update Salesforce
    const processResults = await processBarriers(enrichedBarriers);

    const successCount = processResults.filter(r => r.status === 'success').length;
    const failureCount = processResults.filter(r => r.status === 'failed').length;

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: `Processed ${enrichedBarriers.length} barriers`,
        successCount,
        failureCount,
        partnerAssignments: enrichedBarriers,
        processResults: processResults
      })
    };
  } catch (err) {
    return { statusCode: 500, body: JSON.stringify({ error: String(err) }) };
  }
};

module.exports = { lambdaHandler };

